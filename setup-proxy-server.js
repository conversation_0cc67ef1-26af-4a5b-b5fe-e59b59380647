// Простой HTTP прокси сервер для nginx
// Устанавливает соединение с удаленным сервером через xray HTTP прокси

const http = require('http');
const https = require('https');
const url = require('url');

// Конфигурация
const PORT = 8118;           // Порт для этого прокси сервера
const XRAY_PROXY = {         // Настройки xray HTTP прокси
  host: '127.0.0.1',
  port: 8080
};
const TARGET_HOST = 'kzidpre2-kzidpre2-kzid.ru';  // Целевой хост

// Создаем HTTP прокси сервер
const server = http.createServer((req, res) => {
  console.log(`Получен запрос: ${req.method} ${req.url}`);
  
  // Парсим URL запроса
  const parsedUrl = url.parse(req.url);
  
  // Формируем опции для запроса через xray прокси
  const options = {
    hostname: XRAY_PROXY.host,
    port: XRAY_PROXY.port,
    path: `https://${TARGET_HOST}${req.url}`,
    method: req.method,
    headers: {
      ...req.headers,
      host: TARGET_HOST
    }
  };
  
  console.log(`Проксирую на: ${options.path}`);
  
  // Создаем запрос к xray прокси
  const proxyReq = http.request(options, (proxyRes) => {
    // Копируем заголовки ответа
    Object.keys(proxyRes.headers).forEach(key => {
      res.setHeader(key, proxyRes.headers[key]);
    });
    
    // Устанавливаем статус ответа
    res.writeHead(proxyRes.statusCode);
    
    // Передаем данные ответа
    proxyRes.pipe(res);
  });
  
  // Обработка ошибок
  proxyReq.on('error', (err) => {
    console.error('Ошибка прокси запроса:', err);
    res.writeHead(500);
    res.end(`Proxy Error: ${err.message}`);
  });
  
  // Передаем данные запроса
  req.pipe(proxyReq);
});

// Запускаем сервер
server.listen(PORT, () => {
  console.log(`Прокси сервер запущен на порту ${PORT}`);
  console.log(`Использует xray прокси: ${XRAY_PROXY.host}:${XRAY_PROXY.port}`);
  console.log(`Целевой хост: ${TARGET_HOST}`);
  console.log('Для использования с nginx укажите upstream: server 127.0.0.1:8118');
});
