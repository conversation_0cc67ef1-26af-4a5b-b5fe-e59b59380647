worker_processes  1;

events {
    worker_connections  1024;
}

http {
    # Глобальные настройки таймаутов
    proxy_connect_timeout       300000;
    proxy_send_timeout         300000;
    proxy_read_timeout         300000;
    send_timeout              300000;

    include       mime.types;
    default_type application/json;

    sendfile        on;
    keepalive_timeout  65;

    # Настройки для работы через прокси
    resolver *******;

    # HTTP прокси для конвертации SOCKS в HTTP (privoxy)
    upstream remote_proxy {
        server 127.0.0.1:8118;  # privoxy слушает на 8118 и конвертирует SOCKS в HTTP
    }

    server {
        listen 18080;

        # Глобальные настройки прокси для всех запросов к удаленному серверу
        proxy_redirect off;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Все запросы проксируем через SOCKS прокси к удаленному серверу
        location / {
            # Формируем полный URL для удаленного сервера
            set $remote_url "https://kzidpre2-kzidpre2-kzid.ru$request_uri";
            
            # Проксируем через privoxy (который использует SOCKS прокси xray)
            proxy_pass http://remote_proxy;
            
            # Устанавливаем правильные заголовки для удаленного сервера
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            
            # Передаем cookies (важно для авторизации)
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru localhost;
            proxy_cookie_path / /;
            
            # Указываем целевой URL для HTTP прокси
            proxy_set_header X-Target-URL $remote_url;
            
            # Дополнительные заголовки для совместимости
            proxy_set_header Accept "application/json, text/plain, */*";
            proxy_set_header Accept-Language "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";
            proxy_set_header Cache-Control "no-cache";
            proxy_set_header Pragma "no-cache";
            
            # HTTP версия
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
    }
}

# Инструкция по настройке:
#
# 1. Настройте xray для SOCKS прокси на порту 1080
# 
# 2. Установите и настройте privoxy:
#    sudo apt-get install privoxy
#    
#    Добавьте в /etc/privoxy/config:
#    listen-address 127.0.0.1:8118
#    forward-socks5 / 127.0.0.1:1080 .
#    
#    sudo systemctl restart privoxy
#
# 3. Проверьте работу:
#    curl --proxy http://127.0.0.1:8118 https://kzidpre2-kzidpre2-kzid.ru/kzid_rest/authenticated
#
# 4. Скопируйте cookies из браузера после авторизации на удаленном сайте
#
# 5. Замените nginx.conf на этот файл и перезапустите nginx:
#    sudo nginx -s reload
#
# 6. Запустите фронтенд: npm start
#
# Для возврата к локальному бэкенду просто замените nginx.conf обратно
