worker_processes  1;

events {
    worker_connections  1024;
}

http {
    # Глобальные настройки таймаутов
    proxy_connect_timeout       300000;
    proxy_send_timeout         300000;
    proxy_read_timeout         300000;
    send_timeout              300000;

    include       mime.types;
    default_type application/json;

    sendfile        on;
    keepalive_timeout  65;

    # Настройки для работы через прокси
    resolver *******;

    server {
        listen 18080;

        # Глобальные настройки прокси для всех запросов к удаленному серверу
        proxy_redirect off;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Все запросы проксируем к удаленному серверу
        location / {
            # Используем переменную для динамического proxy_pass
            set $target https://kzidpre2-kzidpre2-kzid.ru$request_uri;
            proxy_pass $target;

            # Устанавливаем правильные заголовки для удаленного сервера
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;

            # Передаем cookies (важно для авторизации)
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru localhost;
            proxy_cookie_path / /;

            # Дополнительные заголовки для совместимости
            proxy_set_header Accept "application/json, text/plain, */*";
            proxy_set_header Accept-Language "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";
            proxy_set_header Cache-Control "no-cache";
            proxy_set_header Pragma "no-cache";
            proxy_set_header User-Agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

            # HTTP версия и SSL настройки
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_ssl_verify off;
            proxy_ssl_server_name on;
        }
    }
}

# Инструкция по настройке:
#
# 1. Настройте xray для HTTP прокси на порту 8080
#    (не SOCKS, а именно HTTP прокси)
#
# 2. Настройте системный прокси для nginx:
#    export http_proxy=http://127.0.0.1:8080
#    export https_proxy=http://127.0.0.1:8080
#
#    Или добавьте в /etc/environment:
#    http_proxy=http://127.0.0.1:8080
#    https_proxy=http://127.0.0.1:8080
#
# 3. Проверьте работу прокси:
#    curl --proxy http://127.0.0.1:8080 https://kzidpre2-kzidpre2-kzid.ru/kzid_rest/authenticated
#
# 4. Скопируйте cookies из браузера после авторизации на удаленном сайте
#
# 5. Замените nginx.conf на этот файл и перезапустите nginx:
#    sudo nginx -s reload
#
# 6. Запустите фронтенд: npm start
#
# Для возврата к локальному бэкенду просто замените nginx.conf обратно
