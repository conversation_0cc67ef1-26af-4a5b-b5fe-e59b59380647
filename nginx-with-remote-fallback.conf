worker_processes  1;

events {
    worker_connections  1024;
}

http {
    # Глобальные настройки таймаутов
    proxy_connect_timeout       300000;
    proxy_send_timeout         300000;
    proxy_read_timeout         300000;
    send_timeout              300000;

    include       mime.types;
    default_type application/json;

    sendfile        on;
    keepalive_timeout  65;

    # Локальные upstream'ы
    upstream krg {
        server 127.0.0.1:18081;
    }

    upstream data_import {
        server 127.0.0.1:18082;
    }

    upstream notify {
        server 127.0.0.1:18087;
    }

    upstream access_control {
        server 127.0.0.1:18084;
    }

    upstream execution_control {
        server 127.0.0.1:18083;
    }

    upstream audit_plan {
        server 127.0.0.1:18085;
    }

    upstream filenet_web {
        server 127.0.0.1:8086;
    }

    # Удаленный upstream через xray туннель
    upstream remote_backend {
        server 127.0.0.1:8090;  # xray туннель к https://kzidpre2-kzidpre2-kzid.ru
    }

    # Проверка доступности удаленного сервера
    map $upstream_http_x_backend_status $use_remote {
        default 0;
        "available" 1;
    }

    server {
        listen 18080;

        proxy_redirect off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Специальный endpoint для проверки удаленного сервера
        location /check-remote {
            proxy_pass http://remote_backend/kzid_rest/authenticated;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            
            # Передача cookies
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # Обработка ошибок
            proxy_intercept_errors on;
            error_page 502 503 504 = @use_local;
        }

        # Fallback на локальный сервер
        location @use_local {
            return 200 '{"status": "local"}';
            add_header Content-Type application/json;
        }

        # Основные API endpoints с автоматическим переключением
        location /kzid_rest/sidebar {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend/kzid_rest/sidebar;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @sidebar_local;
        }

        location @sidebar_local {
            proxy_pass http://krg/kzid_rest/sidebar;
        }

        location ~ ^/kzid_rest/(krg|get_all_presets|notification_list|get_branches|authenticated) {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @krg_local;
        }

        location @krg_local {
            proxy_pass http://krg$request_uri;
        }

        location ~ ^/kzid_rest/replicator {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @replicator_local;
        }

        location @replicator_local {
            proxy_pass http://data_import$request_uri;
        }

        location ~ ^/notify/api/external/ {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @notify_local;
        }

        location @notify_local {
            proxy_pass http://notify$request_uri;
        }

        location ~ ^/kzid_rest/(control_|selector_report_group|document_type|create_role|create_update_group|update_role|delete_role|link_role_permissions|directoryOfOrganization|get_branches|get_ko|get_cooperations|get_cliring|get_professionals|get_ensure|get_micro_org|dictionary/npf) {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @access_control_local;
        }

        location @access_control_local {
            proxy_pass http://access_control$request_uri;
        }

        location ~ ^/kzid_rest/(execution_supervising_nfo|get_all_presets|execution_control_nfo|execution_persons) {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @execution_control_local;
        }

        location @execution_control_local {
            proxy_pass http://execution_control$request_uri;
        }
        
        location ~ ^/execution-control/ {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @execution_control_local;
        }

        location ~ ^/kzid_rest/(offer_summary_plan_nfo|offer_associated_plan|summary_plan_unplanned_audit|unplanned_audits|associated_plan_unplanned_audit|joined_audit_plan) {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend$request_uri;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @audit_plan_local;
        }

        location @audit_plan_local {
            proxy_pass http://audit_plan$request_uri;
        }

        location /filenet/ {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend/filenet/;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @filenet_local;
        }

        location @filenet_local {
            proxy_pass http://filenet_web/filenet/;
        }

        # Все остальные запросы
        location / {
            # Сначала пробуем удаленный сервер
            proxy_pass http://remote_backend;
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
            proxy_cookie_path / /;
            
            # При ошибке переключаемся на локальный
            proxy_intercept_errors on;
            error_page 502 503 504 = @default_local;
        }

        location @default_local {
            proxy_pass http://krg;
        }
    }
}
