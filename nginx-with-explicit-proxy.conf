worker_processes  1;

events {
    worker_connections  1024;
}

http {
    # Глобальные настройки таймаутов
    proxy_connect_timeout       300000;
    proxy_send_timeout         300000;
    proxy_read_timeout         300000;
    send_timeout              300000;

    include       mime.types;
    default_type application/json;

    sendfile        on;
    keepalive_timeout  65;

    # Настройки для работы через прокси
    resolver *******;

    # Локальный HTTP прокси (xray)
    upstream xray_proxy {
        server 127.0.0.1:8080;
    }

    server {
        listen 18080;

        # Глобальные настройки прокси
        proxy_redirect off;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Все запросы проксируем через xray к удаленному серверу
        location / {
            # Проксируем через локальный HTTP прокси
            proxy_pass http://xray_proxy;
            
            # Указываем целевой URL в заголовках
            proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
            proxy_set_header X-Requested-With XMLHttpRequest;
            
            # Передаем cookies
            proxy_set_header Cookie $http_cookie;
            proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru localhost;
            proxy_cookie_path / /;
            
            # Дополнительные заголовки
            proxy_set_header Accept "application/json, text/plain, */*";
            proxy_set_header Accept-Language "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";
            proxy_set_header Cache-Control "no-cache";
            proxy_set_header Pragma "no-cache";
            proxy_set_header User-Agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
            
            # Указываем полный URL для HTTP прокси
            proxy_set_header X-Target-URL "https://kzidpre2-kzidpre2-kzid.ru$request_uri";
            
            # HTTP настройки
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
    }
}

# Примечание: Этот конфиг работает, если ваш xray HTTP прокси поддерживает
# заголовок X-Target-URL или аналогичный механизм указания целевого URL
#
# Альтернативно, можно использовать CONNECT метод для HTTP прокси,
# но это требует более сложной настройки nginx
