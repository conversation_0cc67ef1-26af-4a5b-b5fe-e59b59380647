worker_processes  1;

events {
    worker_connections  1024;
}

http {
    # Глобальные настройки таймаутов
    proxy_connect_timeout       300000;
    proxy_send_timeout         300000;
    proxy_read_timeout         300000;
    send_timeout              300000;

    include       mime.types;
    default_type application/json;

    sendfile        on;
    keepalive_timeout  65;

    # Локальные upstream'ы
    upstream krg {
        server 127.0.0.1:18081;
    }

    upstream data_import {
        server 127.0.0.1:18082;
    }

    upstream notify {
        server 127.0.0.1:18087;
    }

    upstream access_control {
        server 127.0.0.1:18084;
    }

    upstream execution_control {
        server 127.0.0.1:18083;
    }

    upstream audit_plan {
        server 127.0.0.1:18085;
    }

    upstream filenet_web {
        server 127.0.0.1:8086;
    }

    # Удаленный upstream через xray прокси
    # Примечание: xray предоставляет HTTP/SOCKS прокси, а не прямой туннель
    upstream remote_backend {
        server kzidpre2-kzidpre2-kzid.ru:443;  # Прямое обращение к удаленному серверу через прокси
    }

    # Переменная для переключения между локальным и удаленным бэкендом
    # Измените значение на "remote" для использования удаленного бэкенда
    # Измените значение на "local" для использования локального бэкенда
    map $arg_backend $backend_type {
        default "local";  # По умолчанию используем локальный бэкенд
        "remote" "remote";
        "local" "local";
    }

    # Можно также использовать cookie для переключения
    map $cookie_backend $backend_from_cookie {
        default $backend_type;
        "remote" "remote";
        "local" "local";
    }

    server {
        listen 18080;

        proxy_redirect off;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Endpoint для переключения режима через cookie
        location /switch-backend {
            add_header Set-Cookie "backend=$arg_mode; Path=/; Max-Age=86400";
            return 200 '{"status": "switched to $arg_mode"}';
            add_header Content-Type application/json;
        }

        # Sidebar
        location /kzid_rest/sidebar {
            if ($backend_from_cookie = "remote") {
                proxy_pass https://kzidpre2-kzidpre2-kzid.ru/kzid_rest/sidebar;
                proxy_http_version 1.1;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;

                # Настройки для работы через HTTP прокси (если xray предоставляет HTTP прокси)
                # Раскомментируйте следующие строки, если используете HTTP прокси:
                # proxy_set_header Proxy-Connection "";
                # proxy_pass_request_headers on;

                # Настройки SSL
                proxy_ssl_verify off;
                proxy_ssl_server_name on;
                break;
            }
            proxy_pass http://krg/kzid_rest/sidebar;
            proxy_set_header Host $host;
        }

        # KRG endpoints
        location ~ ^/kzid_rest/(krg|get_all_presets|notification_list|get_branches|authenticated) {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://krg$request_uri;
            proxy_set_header Host $host;
        }

        # Replicator
        location ~ ^/kzid_rest/replicator {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://data_import$request_uri;
            proxy_set_header Host $host;
        }

        # Notify
        location ~ ^/notify/api/external/ {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://notify$request_uri;
            proxy_set_header Host $host;
        }

        # Access Control
        location ~ ^/kzid_rest/(control_|selector_report_group|document_type|create_role|create_update_group|update_role|delete_role|link_role_permissions|directoryOfOrganization|get_branches|get_ko|get_cooperations|get_cliring|get_professionals|get_ensure|get_micro_org|dictionary/npf) {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://access_control$request_uri;
            proxy_set_header Host $host;
        }

        # Execution Control
        location ~ ^/kzid_rest/(execution_supervising_nfo|get_all_presets|execution_control_nfo|execution_persons) {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://execution_control$request_uri;
            proxy_set_header Host $host;
        }

        location ~ ^/execution-control/ {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://execution_control$request_uri;
            proxy_set_header Host $host;
        }

        # Audit Plan
        location ~ ^/kzid_rest/(offer_summary_plan_nfo|offer_associated_plan|summary_plan_unplanned_audit|unplanned_audits|associated_plan_unplanned_audit|joined_audit_plan) {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend$request_uri;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://audit_plan$request_uri;
            proxy_set_header Host $host;
        }

        # FileNet
        location /filenet/ {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend/filenet/;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://filenet_web/filenet/;
            proxy_set_header Host $host;
        }

        # Все остальные запросы
        location / {
            if ($backend_from_cookie = "remote") {
                proxy_pass http://remote_backend;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_cookie_domain kzidpre2-kzidpre2-kzid.ru $host;
                proxy_cookie_path / /;
                break;
            }
            proxy_pass http://krg;
            proxy_set_header Host $host;
        }
    }
}
