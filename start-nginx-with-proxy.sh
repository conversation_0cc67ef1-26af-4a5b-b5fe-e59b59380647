#!/bin/bash
# Скрипт для запуска nginx с HTTP прокси (только для nginx)

# Устанавливаем прокси только для этого процесса
export http_proxy=http://127.0.0.1:8080
export https_proxy=http://127.0.0.1:8080

# Запускаем nginx с удаленной конфигурацией
echo "Starting nginx with remote backend configuration..."
nginx -c $(pwd)/nginx-remote-only.conf

echo "Nginx started with proxy settings:"
echo "http_proxy=$http_proxy"
echo "https_proxy=$https_proxy"
