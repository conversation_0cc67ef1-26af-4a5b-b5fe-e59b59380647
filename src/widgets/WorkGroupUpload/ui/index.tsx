import { Spin, Typography } from 'antd';
import type { FC } from 'react';
import { useState } from 'react';
import {
  WorkGroupUploadProps,
  workGroupUploadStore,
  WorkGroupUploadInnerProps,
  WGUploadConfig,
} from 'widgets/WorkGroupUpload';
import { newlazyTree, TreeKeyProvider } from 'features/NewLazyTree';
import { UploadFileList } from 'features/UploadFileList';
import { apiUrls } from 'shared/api';
import { APP_DOWNLOAD_MANAGER } from 'shared/config/constants';
import { treeParserByConfig } from 'shared/lib';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { AppPopup, EmptyFileList } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { EPCTree } from './EPCTree';
import { SourceSelector } from './SourceSelector';
import styles from './styles.module.scss';
import { Uploader } from './Uploader';

const UPLOAD_TREE_KEY = 'workGroupUpload';

const ENDPOINTS = {
  fetch: apiUrls.workGroup.EPC.getLazyTreePage,
  delete: apiUrls.workGroup.fileData.deleteFileById,
  move: apiUrls.workGroup.EPC.saveMove,
};

const transformResponse = (
  response: {
    data: {
      treeData: TreeElement[];
      pagination?: { pageNumber: number; pageSize: number; total: number };
      parentInfo?: { totalCountOfLeafs: number };
    };
  },
  parentId?: string,
): TreeElement[] => {
  const { treeParserConfig } = WGUploadConfig;

  const nodes =
    treeParserByConfig(response.data.treeData, treeParserConfig) || [];

  if (response.data.pagination && parentId) {
    if (nodes.length > 0) {
      const { total, pageNumber, pageSize } = response.data.pagination;
      nodes[0].total = total;
      nodes[0].page = pageNumber;
      nodes[0].size = pageSize;

      if (response.data.parentInfo?.totalCountOfLeafs !== undefined) {
        nodes[0].parentTotalCountOfLeafs =
          response.data.parentInfo.totalCountOfLeafs;
      }
    }
  }

  return nodes;
};

const WorkGroupUploadInner: FC<WorkGroupUploadInnerProps> = ({
  cabinetId,
  refetch,
  onClose,
  isDisabled,
}) => {
  const upload = useAppSelector(
    workGroupUploadStore.selectors.uploadFilesSelector,
  );

  const lazyTreeParams = {
    treeKey: UPLOAD_TREE_KEY,
    endpoints: ENDPOINTS,
    queryParams: {
      cabinetId,
      isCabinet: true,
    },
    transformResponse,
    isPaginationEnabled: true,
  };

  const { treeData, loadData, loadMoreCatalogItems, statuses, refetchNode } =
    newlazyTree.hooks.useTreeData(lazyTreeParams);

  const treeKeysProps = newlazyTree.hooks.useTreeKeys(UPLOAD_TREE_KEY);

  const { handleDeleteFile, handleCheckFiles, handleCheckAllFiles } =
    useCreateSliceActions(workGroupUploadStore.reducers.slice.actions);

  const [isDownloaderPending, setDownloaderPending] = useState(false);
  const [source, setSource] = useState<number | null>(null);
  const [checkedNode, setCheckedNode] = useState<TreeElement | null>(null);

  const refetchNodeAdapter = async (
    node: TreeElement,
  ): Promise<TreeElement[]> => {
    await refetchNode(node.itemId!);
    return [];
  };

  const [handleUpload, uploadState] = workGroupUploadStore.hooks.useUploadFiles(
    cabinetId || '',
    source,
    onClose,
    refetch,
    checkedNode!,
    handleDeleteFile,
    refetchNodeAdapter,
    setCheckedNode,
  );

  const actionButtons = workGroupUploadStore.hooks.useButtons(
    handleUpload,
    source,
    onClose,
    cabinetId || '',
    checkedNode,
    isDisabled,
    (status) => setDownloaderPending(status),
  );

  return (
    <Spin
      spinning={isDownloaderPending}
      tip={
        <Typography.Title level={2} type="secondary" className={styles.text}>
          Идет запрос на открытие приложения <br />
          &quot;{APP_DOWNLOAD_MANAGER}&quot;
        </Typography.Title>
      }
      size="large"
      wrapperClassName={styles.container}
    >
      <SourceSelector
        source={source}
        handleSelect={(value) => setSource(value)}
      />

      <div className={styles.upload}>
        <Uploader>
          {upload.files.data.length === 0 ? (
            <EmptyFileList />
          ) : (
            <UploadFileList
              uploadState={uploadState}
              fileArr={upload.files.data}
              checkable={{
                isPending: upload.files.isPending,
                error: upload.files.error,
                handleCheckFiles,
                handleCheckAllFiles,
              }}
              handleDeleteFile={handleDeleteFile}
            />
          )}
        </Uploader>

        <EPCTree
          handleCheck={setCheckedNode}
          checkedNode={checkedNode}
          treeData={treeData}
          loadData={loadData}
          loadMoreCatalogItems={loadMoreCatalogItems}
          statuses={{
            error: statuses.error as AppError | null,
            isLoading: statuses.isLoading,
            loadingBranch: statuses.loadingBranch,
            loadingKeys: statuses.loadingKeys,
          }}
          treeKeysProps={treeKeysProps}
          isPending={statuses.isLoading}
        />
      </div>

      <ButtonsContainer buttons={actionButtons} />
    </Spin>
  );
};

export const WorkGroupUpload: FC<WorkGroupUploadProps> = ({
  onClose,
  isOpened,
  ...props
}) => (
  <TreeKeyProvider>
    <AppPopup
      isOpened={isOpened}
      onClose={onClose}
      className={styles.popup}
      title="Загрузка файлов в ЭПП"
    >
      <WorkGroupUploadInner onClose={onClose} {...props} />
    </AppPopup>
  </TreeKeyProvider>
);
