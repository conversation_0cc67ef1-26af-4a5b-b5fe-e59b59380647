import { Tree, TreeProps, Button } from 'antd';
import { FC, useCallback } from 'react';
import { workGroupUploadStore } from 'widgets/WorkGroupUpload';
import { LoadData } from 'features/NewLazyTree/types';
import { renderTreeTitle, useAppSelector } from 'shared/model';
import { ApiContainer, BorderedFieldset } from 'shared/ui';
import styles from './styles.module.scss';

interface EPCTreeProps {
  checkedNode: TreeElement | null;
  handleCheck: (node: TreeElement | null) => void;
  isPending: boolean;
  loadData: LoadData;
  loadMoreCatalogItems: (parentId: string) => Promise<void>;
  statuses: {
    error: AppError | null;
    isLoading: boolean;
    loadingBranch: boolean;
    loadingKeys: Record<string, boolean>;
  };
  treeData: TreeElement[];
  treeKeysProps: {
    checkedKeys: Key[];
    expandedKeys: Key[];
    loadedKeys: Key[];
    onCheck: TreeProps<TreeElement>['onCheck'];
    onExpand: TreeProps<TreeElement>['onExpand'];
    onSelect: TreeProps<TreeElement>['onSelect'];
    selectedKeys: Key[];
  };
}

const titleRenderConfig = {
  isSelected: false,
  showCustomFolders: false,
  isLinked: false,
  isKVIconVisible: true,
};

export const EPCTree: FC<EPCTreeProps> = ({
  handleCheck,
  checkedNode,
  treeData,
  loadData,
  loadMoreCatalogItems,
  statuses,
  treeKeysProps,
  isPending,
}) => {
  const data = useAppSelector(
    workGroupUploadStore.selectors.uploadFilesSelector,
  );

  type CheckedKeys =
    | React.Key[]
    | {
        checked: React.Key[];
        halfChecked: React.Key[];
      };

  type OnCheckCallback = (
    checked: CheckedKeys,
    info: { node: TreeElement },
  ) => void;

  const onCheck: OnCheckCallback = useCallback(
    (_, info) => {
      if (info.node.itemId === checkedNode?.itemId) {
        handleCheck(null);
      } else {
        handleCheck(info.node);
      }
    },
    [checkedNode?.itemId, handleCheck],
  );

  const titleRender = useCallback(
    (node: TreeElement) => {
      // Обработка элемента "Загрузить еще"
      if (node.isLoadMoreButton) {
        const loadMoreKey = `load-more-${node.parentId}`;
        const isLoading = statuses.loadingKeys[loadMoreKey];

        return (
          <Button
            loading={isLoading}
            className={styles.loadMoreButton}
            onClick={() => {
              if (node.parentId) {
                loadMoreCatalogItems(node.parentId);
              }
            }}
            type="link"
            block
          >
            {node.title}
          </Button>
        );
      }

      // Обычная обработка узлов
      return renderTreeTitle(
        node,
        titleRenderConfig.isSelected,
        titleRenderConfig.showCustomFolders,
        titleRenderConfig.isLinked,
        titleRenderConfig.isKVIconVisible,
      );
    },
    [loadMoreCatalogItems, statuses.loadingKeys],
  );

  return (
    <BorderedFieldset title="ЭПП">
      <ApiContainer error={statuses.error} isPending={isPending}>
        <Tree
          treeData={treeData}
          loadedKeys={treeKeysProps.loadedKeys}
          loadData={loadData}
          expandedKeys={treeKeysProps.expandedKeys}
          onExpand={treeKeysProps.onExpand}
          checkable
          disabled={data.files.isPending}
          checkedKeys={[checkedNode?.key || '']}
          checkStrictly
          onCheck={onCheck}
          className={styles.tree}
          selectable={false}
          showLine
          titleRender={titleRender}
        />
      </ApiContainer>
    </BorderedFieldset>
  );
};
