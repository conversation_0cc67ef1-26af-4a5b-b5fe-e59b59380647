import { useState, useCallback, useMemo, useEffect } from 'react';
import {
  NWGLib,
  TabData,
  UseLazyTabsDataReturn,
} from 'widgets/NewWorkGroupTabs';
import { TabsWithLazyTable } from 'features/DataGrid';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

const createInitialTabsData = (tabs: TabsWithLazyTable[]): TabData[] =>
  tabs.map((tab) => ({
    ...tab,
    tabStatus: tab.tableData
      ? {
          isLoading: false,
          isLoaded: true,
          isError: false,
        }
      : {
          isLoading: false,
          isLoaded: false,
          isError: false,
        },
  }));

interface UseLazyTabsDataOptions {
  activeTabKeys?: string[];
  onActiveTabKeysChange?: (keys: string[]) => void;
}

export const useLazyTabsData = (
  initialTabs: TabsWithLazyTable[],
  parentRowId: string | number | undefined,
  options?: UseLazyTabsDataOptions,
): UseLazyTabsDataReturn => {
  const [getTabDataRequest] =
    useAxiosRequest<TableColumnsAndRowsWithPagination>();
  const [internalActiveTabKeys, setInternalActiveTabKeys] = useState<string[]>(
    [],
  );
  const { activeTabKeys: externalActiveTabKeys, onActiveTabKeysChange } =
    options || {};

  const activeTabKeys = externalActiveTabKeys ?? internalActiveTabKeys;
  const setActiveTabKeys = onActiveTabKeysChange ?? setInternalActiveTabKeys;

  const [tabsData, setTabsData] = useState<TabData[]>(() =>
    createInitialTabsData(initialTabs),
  );

  // Обновляем tabsData при изменении initialTabs
  useEffect(() => {
    setTabsData((prevTabsData) => {
      const newTabsData = createInitialTabsData(initialTabs);

      // Сохраняем состояние загрузки для ленивых табов
      return newTabsData.map((newTab) => {
        const existingTab = prevTabsData.find((tab) => tab.key === newTab.key);

        // Если таб был preloaded, обновляем его данные
        if (newTab.tableData) {
          return newTab;
        }

        // Для ленивых табов сохраняем предыдущее состояние
        return existingTab || newTab;
      });
    });
  }, [initialTabs]);

  const isPreloadedTab = useCallback(
    (tabKey: string): boolean => {
      const originalTab = initialTabs.find((t) => t.key === tabKey);
      return !!originalTab?.tableData;
    },
    [initialTabs],
  );

  const updateTabState = useCallback(
    (tabKey: string, updates: Partial<TabData>): void => {
      setTabsData((prev) =>
        prev.map((tab) => (tab.key === tabKey ? { ...tab, ...updates } : tab)),
      );
    },
    [],
  );

  const loadTabData = useCallback(
    async (tabKey: string, page = 1): Promise<void> => {
      if (isPreloadedTab(tabKey)) {
        return;
      }

      if (!parentRowId) {
        return;
      }

      const tab = tabsData.find((t) => t.key === tabKey);
      if (!tab || tab.tabStatus?.isLoading) {
        return;
      }

      updateTabState(tabKey, {
        tabStatus: {
          isLoading: true,
          isLoaded: false,
          isError: false,
        },
      });

      try {
        const params = NWGLib.getTabRequestParams(
          tab.endpoint,
          parentRowId,
          page,
        );
        const url = generateUrlWithQueryParams(tab.endpoint, params);
        const result = await getTabDataRequest(url, { method: 'GET' });

        updateTabState(tabKey, {
          tableData: result,
          pagination: {
            currentPage: page,
            pageSize: result?.pagination?.pageSize || 10,
            total: result?.pagination?.total || result?.rows?.length || 0,
          },
          tabStatus: {
            isLoading: false,
            isLoaded: true,
            isError: false,
          },
        });
      } catch (error) {
        appErrorNotification(
          'Произошла ошибка при загрузке вложенной таблицы',
          error as AppError,
        );

        updateTabState(tabKey, {
          tabStatus: {
            isLoading: false,
            isLoaded: false,
            isError: true,
          },
        });
      }
    },
    [tabsData, parentRowId, isPreloadedTab, updateTabState, getTabDataRequest],
  );

  const handleTabToggle = useCallback(
    async (keys: string | string[]): Promise<void> => {
      const keysArray = Array.isArray(keys) ? keys : [keys];
      setActiveTabKeys(keysArray);

      const loadPromises = keysArray
        .filter((tabKey) => {
          const tab = tabsData.find((t) => t.key === tabKey);
          return (
            !isPreloadedTab(tabKey) &&
            tab &&
            (!tab.tabStatus?.isLoaded || tab.tabStatus?.isError)
          );
        })
        .map((tabKey) => loadTabData(tabKey));

      await Promise.all(loadPromises);
    },
    [tabsData, isPreloadedTab, loadTabData, setActiveTabKeys],
  );

  const handlePageChange = useCallback(
    async (tab: TabData, page: number): Promise<void> => {
      if (isPreloadedTab(String(tab.key))) {
        return;
      }
      await loadTabData(String(tab.key), page);
    },
    [isPreloadedTab, loadTabData],
  );

  const memoizedReturn = useMemo(
    () => ({
      tabsData,
      activeTabKeys,
      loadTabData,
      handleTabToggle,
      handlePageChange,
    }),
    [tabsData, activeTabKeys, loadTabData, handleTabToggle, handlePageChange],
  );

  return memoizedReturn;
};
