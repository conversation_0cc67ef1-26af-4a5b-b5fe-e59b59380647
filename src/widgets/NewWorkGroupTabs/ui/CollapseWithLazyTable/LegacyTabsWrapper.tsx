import { useEffect, useState, useCallback } from 'react';
import type {
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
} from 'widgets/NewWorkGroupTabs';
import { TableRowData, NestedTabsWithLazyTable } from 'features/DataGrid';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';
import { ApiContainer } from 'shared/ui';

interface LegacyTabsWrapperProps {
  collapseWithLazyTable: (
    nestedContent: NestedTabsWithLazyTable,
    customRowRender: CustomRowRender,
    row: TableRowData,
    refetch: Callback,
    togglePopup: TogglePopup,
    handleRow: HandleRow,
    activeEndpoint: Endpoint,
    permissions: Permissions,
    isRequest?: boolean,
  ) => React.ReactNode;
  customRowRender: CustomRowRender;
  endpoint: Endpoint;
  handleRow: HandleRow;
  permissions: Permissions;
  refetch: Callback;
  row: TableRowData;
  togglePopup: TogglePopup;
  cabinetId?: string;
  isRequest?: boolean;
}

export const LegacyTabsWrapper: React.FC<LegacyTabsWrapperProps> = ({
  cabinetId,
  row,
  endpoint,
  collapseWithLazyTable,
  customRowRender,
  refetch,
  togglePopup,
  handleRow,
  permissions,
  isRequest,
}) => {
  const [tabs, setTabs] = useState<NestedTabsWithLazyTable | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fetchTabs] = useAxiosRequest<NestedTabsWithLazyTable>();

  const loadTabs = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      if (endpoint === 'krg3_request_notice' && row.rowId?.id) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = row.rowId.id;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const result = await fetchTabs(url, { method: 'POST' });

      if (result && result.tabs) {
        setTabs(result);
      } else {
        setError('Некорректный формат данных');
      }
    } catch (err) {
      setError('Ошибка загрузки табов');
      appErrorNotification('Ошибка загрузки табов', err as AppError);
    } finally {
      setLoading(false);
    }
  }, [endpoint, row.rowId?.id, cabinetId, fetchTabs]);

  useEffect(() => {
    // Загружаем табы только если их еще нет
    if (!row.nestedTable && !tabs) {
      loadTabs();
    } else if (row.nestedTable) {
      setTabs(row.nestedTable as NestedTabsWithLazyTable);
      setLoading(false);
    }
  }, [row.key, row.nestedTable, tabs, loadTabs]);

  return (
    <ApiContainer
      error={Boolean(error)}
      isPending={loading}
      errorTitle={error || 'Ошибка загрузки вложенных данных'}
      refresh={() => {
        setError(null);
        loadTabs();
      }}
    >
      {tabs &&
        collapseWithLazyTable(
          tabs,
          customRowRender,
          row,
          refetch,
          togglePopup,
          handleRow,
          endpoint,
          permissions,
          isRequest,
        )}
    </ApiContainer>
  );
};
