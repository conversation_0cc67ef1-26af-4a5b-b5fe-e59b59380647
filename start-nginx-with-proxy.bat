@echo off
REM Скрипт для запуска nginx с HTTP прокси (только для nginx)

REM Устанавливаем прокси только для этого процесса
set http_proxy=http://127.0.0.1:8080
set https_proxy=http://127.0.0.1:8080

REM Запускаем nginx с удаленной конфигурацией
echo Starting nginx with remote backend configuration...
nginx.exe -c nginx-remote-only.conf

echo Nginx started with proxy settings
echo http_proxy=%http_proxy%
echo https_proxy=%https_proxy%
pause
