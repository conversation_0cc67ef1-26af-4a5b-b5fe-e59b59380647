# Конфигурация nginx для работы через SOCKS прокси
# Требует модуль nginx-module-stream или использование внешнего HTTP прокси

worker_processes  1;

events {
    worker_connections  1024;
}

http {
    # Глобальные настройки таймаутов
    proxy_connect_timeout       300000;
    proxy_send_timeout         300000;
    proxy_read_timeout         300000;
    send_timeout              300000;

    include       mime.types;
    default_type application/json;

    sendfile        on;
    keepalive_timeout  65;

    # Настройки для работы через прокси
    resolver 8.8.8.8;

    # Локальные upstream'ы
    upstream krg {
        server 127.0.0.1:18081;
    }

    upstream data_import {
        server 127.0.0.1:18082;
    }

    upstream notify {
        server 127.0.0.1:18087;
    }

    upstream access_control {
        server 127.0.0.1:18084;
    }

    upstream execution_control {
        server 127.0.0.1:18083;
    }

    upstream audit_plan {
        server 127.0.0.1:18085;
    }

    upstream filenet_web {
        server 127.0.0.1:8086;
    }

    # HTTP прокси для конвертации SOCKS в HTTP
    # Запустите отдельно: privoxy или polipo для конвертации SOCKS в HTTP
    upstream http_proxy {
        server 127.0.0.1:8118;  # privoxy по умолчанию слушает на 8118
    }

    # Переменная для переключения между локальным и удаленным бэкендом
    map $arg_backend $backend_type {
        default "local";
        "remote" "remote";
        "local" "local";
    }

    map $cookie_backend $backend_from_cookie {
        default $backend_type;
        "remote" "remote";
        "local" "local";
    }

    server {
        listen 18080;

        proxy_redirect off;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Endpoint для переключения режима
        location /switch-backend {
            add_header Set-Cookie "backend=$arg_mode; Path=/; Max-Age=86400";
            return 200 '{"status": "switched to $arg_mode", "mode": "$arg_mode", "note": "Requires HTTP proxy (privoxy/polipo) for SOCKS conversion"}';
            add_header Content-Type application/json;
        }

        # Проверка текущего режима
        location /backend-status {
            return 200 '{"current_backend": "$backend_from_cookie", "proxy_type": "socks_via_http"}';
            add_header Content-Type application/json;
        }

        # Sidebar
        location /kzid_rest/sidebar {
            if ($backend_from_cookie = "remote") {
                # Используем HTTP прокси для доступа через SOCKS
                proxy_pass http://http_proxy;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                
                # Указываем целевой URL в заголовке для HTTP прокси
                proxy_set_header X-Target-URL "https://kzidpre2-kzidpre2-kzid.ru/kzid_rest/sidebar";
                
                # Альтернативный способ - через переменную
                set $target_url "https://kzidpre2-kzidpre2-kzid.ru/kzid_rest/sidebar";
                proxy_pass $target_url;
                
                break;
            }
            proxy_pass http://krg/kzid_rest/sidebar;
            proxy_set_header Host $host;
        }

        # KRG endpoints
        location ~ ^/kzid_rest/(krg|get_all_presets|notification_list|get_branches|authenticated) {
            if ($backend_from_cookie = "remote") {
                set $target_url "https://kzidpre2-kzidpre2-kzid.ru$request_uri";
                proxy_pass http://http_proxy;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_set_header X-Target-URL $target_url;
                break;
            }
            proxy_pass http://krg$request_uri;
            proxy_set_header Host $host;
        }

        # Все остальные endpoints аналогично...
        # (сокращено для краткости, принцип тот же)

        # Все остальные запросы
        location / {
            if ($backend_from_cookie = "remote") {
                set $target_url "https://kzidpre2-kzidpre2-kzid.ru$request_uri";
                proxy_pass http://http_proxy;
                proxy_set_header Host kzidpre2-kzidpre2-kzid.ru;
                proxy_set_header X-Requested-With XMLHttpRequest;
                proxy_set_header Cookie $http_cookie;
                proxy_set_header X-Target-URL $target_url;
                break;
            }
            proxy_pass http://krg;
            proxy_set_header Host $host;
        }
    }
}

# Примечание: Для работы с SOCKS прокси нужен дополнительный HTTP прокси
# 
# Установка и настройка privoxy:
# 1. Установите privoxy: apt-get install privoxy (Ubuntu/Debian)
# 2. Настройте /etc/privoxy/config:
#    listen-address 127.0.0.1:8118
#    forward-socks5 / 127.0.0.1:1080 .
# 3. Перезапустите privoxy: systemctl restart privoxy
#
# Где 127.0.0.1:1080 - адрес вашего SOCKS прокси от xray
